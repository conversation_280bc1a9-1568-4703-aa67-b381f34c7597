cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 1500,
        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,
        // 最大跳跃次数
        maxJumpTimes: 5,
        // 玩家在X轴的固定位置
        fixedPositionX: -200,
        // 滑行时的碰撞体高度缩放
        slideColliderScale: 0.5,

    },

    onLoad() {
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;
        this.slideTimer = null; // 用于存储滑行定时器

        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);
        // 获取动画组件
        this.animationComponent = this.getComponent(cc.Animation);
        // 获取碰撞体组件 
        this.collider = this.getComponent(cc.PhysicsBoxCollider);

        // if (this.collider) {
        //     this.originalColliderSize = this.collider.size.clone();
        // } else {
        //     console.error("未找到PhysicsBoxCollider组件！");
        //     this.originalColliderSize = null;
        // }
        // 记录初始位置
        // this.initialPosition = cc.v2(this.node.x, this.node.y);
        // 绑定碰撞事件
        this.node.on('onBeginContact', this.onBeginContact, this);
    },

    update() {
        // 保持X轴位置不变
        this.node.x = this.fixedPositionX;
        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.node.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
    },

    jump() {
        if (this.isGameOver) {
            return;
        }
        // 如果正在滑行，立即结束滑行
        if (this.isSliding) {
            this.endSlide();
        }
        // 跳跃条件：在地面上或者还有剩余跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);
            // 增加跳跃计数
            this.jumpCount++;
            // 标记不在地面上
            this.isOnGround = false;
            if (this.animationComponent) {
                this.animationComponent.play('playerjump');
                // 设置定时器切换回默认动画
                this.scheduleOnce(() => {
                    this.animationComponent.play('playeridle');
                }, 0.5); // 跳跃动画持续时间
            }
        }
    },

    slide() {
        if (this.isGameOver || this.isSliding || !this.isOnGround) {
            return;
        }
        this.isSliding = true;
        this.animationComponent.play('playerslide');

        // 设置滑行结束定时器
        this.slideTimer = this.scheduleOnce(() => {
            this.endSlide();
        }, 0.3); // 滑行持续时间
    },

    endSlide() {
        if (!this.isSliding) {
            return;
        }

        // 清除滑行定时器
        if (this.slideTimer) {
            this.unschedule(this.slideTimer);
            this.slideTimer = null;
        }

        // 重置滑行状态
        this.isSliding = false;
        this.animationComponent.play('playeridle');
    },

    onBeginContact(contact, selfCollider, otherCollider) {
        // 简化地面检测：只要碰到地面且玩家向下运动或静止，就认为落地
            if (this.rigidBody.linearVelocity.y <= 100) {
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
                // console.log("碰撞检测：角色落地");
            }
    },
    gameOver() {
        this.isGameOver = true;
    },
    onDestroy() {
        // 移除碰撞事件监听
        this.node.off('onBeginContact', this.onBeginContact, this);
        // 取消所有定时器
        this.unscheduleAllCallbacks();
    }
});